import type { Package } from '@s4tk/models';
import type { IResourceProcessor, ProcessedResource, ResourceProcessingOptions, ResourceProcessingResult } from './types';
import { GenericResourceProcessor } from './GenericResourceProcessor';
import { SimDataProcessor } from './SimDataProcessor';
import { StringTableProcessor } from './StringTableProcessor';
import { TuningProcessor } from './TuningProcessor';
import { ImageProcessor } from './ImageProcessor';
import { RESOURCE_GROUPS } from '../../../constants/ResourceTypeRegistry';
import { UnifiedErrorHandler, UnifiedErrorCategory } from '../../shared/UnifiedErrorHandler';

/**
 * Main resource processor that orchestrates specialized processors
 * Refactored to use the Strategy pattern with specialized processors
 */
export class ResourceProcessor {
    private processors: Map<number, IResourceProcessor> = new Map();
    private fallbackProcessor: IResourceProcessor;
    
    constructor() {
        this.fallbackProcessor = new GenericResourceProcessor();
        this.registerProcessors();
    }
    
    /**
     * Processes all resources in a package using appropriate specialized processors
     */
    public process(s4tkPackage: Package, options?: ResourceProcessingOptions): ProcessedResource[] {
        const startTime = Date.now();
        const results: ProcessedResource[] = [];
        const errors: string[] = [];
        
        try {
            for (const entry of s4tkPackage.entries.values()) {
                try {
                    const processor = this.getProcessor(entry.key.type);
                    const processedResource = processor.process(entry, options);
                    results.push(processedResource);
                } catch (error) {
                    const unifiedError = UnifiedErrorHandler.createError(
                        error,
                        'ResourceProcessor.process',
                        entry.id?.toString() || 'unknown',
                        UnifiedErrorCategory.RESOURCE_PARSING
                    );

                    errors.push(unifiedError.message);

                    // Add error resource
                    results.push({
                        id: entry.id?.toString() || 'unknown',
                        type: 'Error',
                        size: 0,
                        compressed: false,
                        metadata: { error: unifiedError.message },
                        issues: [unifiedError.message]
                    });
                }
            }
            
            return results;
            
        } catch (error) {
            errorHandler.handleS4TKError(error, 'ResourceProcessor.process', 'package');
            return [];
        }
    }
    
    /**
     * Processes resources asynchronously with better performance
     */
    public async processAsync(s4tkPackage: Package, options?: ResourceProcessingOptions): Promise<ResourceProcessingResult> {
        const startTime = Date.now();
        const results: ProcessedResource[] = [];
        const errors: string[] = [];
        const warnings: string[] = [];
        
        try {
            // Process resources in batches to avoid overwhelming the system
            const entries = Array.from(s4tkPackage.entries.values());
            const batchSize = 10;
            
            for (let i = 0; i < entries.length; i += batchSize) {
                const batch = entries.slice(i, i + batchSize);
                
                const batchPromises = batch.map(async (entry) => {
                    try {
                        const processor = this.getProcessor(entry.key.type);
                        return await processor.process(entry, options);
                    } catch (error) {
                        const errorInfo = errorHandler.handleS4TKError(
                            error,
                            'ResourceProcessor.processAsync',
                            entry.id?.toString() || 'unknown'
                        );
                        
                        errors.push(errorInfo.message);
                        
                        return {
                            id: entry.id?.toString() || 'unknown',
                            type: 'Error',
                            size: 0,
                            compressed: false,
                            metadata: { error: errorInfo.message },
                            issues: [errorInfo.message]
                        };
                    }
                });
                
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
            }
            
            return {
                resources: results,
                totalProcessed: results.length,
                processingTime: Date.now() - startTime,
                errors,
                warnings
            };
            
        } catch (error) {
            errorHandler.handleS4TKError(error, 'ResourceProcessor.processAsync', 'package');
            
            return {
                resources: [],
                totalProcessed: 0,
                processingTime: Date.now() - startTime,
                errors: [error.message],
                warnings
            };
        }
    }
    
    /**
     * Registers specialized processors for different resource types
     */
    private registerProcessors(): void {
        // Register SimData processor
        const simDataProcessor = new SimDataProcessor();
        this.processors.set(0x545AC67A, simDataProcessor); // SimData type

        // Register StringTable processor
        const stringTableProcessor = new StringTableProcessor();
        this.processors.set(0x220557DA, stringTableProcessor); // StringTable type

        // Register Tuning processor
        const tuningProcessor = new TuningProcessor();
        this.processors.set(0xE6BBD73D, tuningProcessor); // CombinedTuning type

        // Register Image processor for texture resources
        const imageProcessor = new ImageProcessor();
        for (const textureType of RESOURCE_GROUPS.TEXTURE_RESOURCES) {
            this.processors.set(textureType, imageProcessor);
        }
    }
    
    /**
     * Gets the appropriate processor for a resource type
     */
    private getProcessor(resourceType: number): IResourceProcessor {
        return this.processors.get(resourceType) || this.fallbackProcessor;
    }
    
    /**
     * Registers a custom processor for specific resource types
     */
    public registerProcessor(resourceTypes: number[], processor: IResourceProcessor): void {
        resourceTypes.forEach(type => {
            this.processors.set(type, processor);
        });
    }
    
    /**
     * Gets statistics about registered processors
     */
    public getProcessorStats(): {
        totalProcessors: number;
        registeredTypes: number[];
        processorNames: string[];
    } {
        return {
            totalProcessors: this.processors.size + 1, // +1 for fallback
            registeredTypes: Array.from(this.processors.keys()),
            processorNames: [
                ...Array.from(this.processors.values()).map(p => p.getProcessorName()),
                this.fallbackProcessor.getProcessorName()
            ]
        };
    }
}