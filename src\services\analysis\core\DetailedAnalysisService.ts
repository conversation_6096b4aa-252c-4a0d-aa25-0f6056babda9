import { Package } from '@s4tk/models';
import type { ResourceEntry } from '@s4tk/models/types';
import { FileType, ModCategory, type StringTableData } from '../../../types/analysis';
import { FileTypeDetector } from './FileTypeDetector';
import { DeepAnalyzer } from '../deep/DeepAnalyzer';
import { ResourceProcessor } from '../resources/ResourceProcessor';
import { ScriptAnalyzer } from '../resources/analyzers/ScriptAnalyzer';
import { ManifestAnalyzer } from '../specialized/common/ManifestAnalyzer';
import { FilenameMetadataExtractor } from '../specialized/common/FilenameMetadataExtractor';
import { DependencyAnalyzer } from '../specialized/common/DependencyAnalyzer';
import { ResourceIntelligenceAnalyzer } from '../specialized/common/ResourceIntelligenceAnalyzer';
import { QualityAssessmentAnalyzer } from '../specialized/common/QualityAssessmentAnalyzer';
import { ScriptIntelligenceAnalyzer } from '../specialized/common/ScriptIntelligenceAnalyzer';
import { StringTableProcessor } from '../resources/StringTableProcessor';
import { EnhancedMetadataExtractionService } from '../metadata/EnhancedMetadataExtractionService';
import { ContentAnalysisService } from '../content/ContentAnalysisService';
import { BinaryResourceType } from '@s4tk/models/enums';
import { packageManager, errorHandler, type SelectiveLoadingOptions } from '../../s4tk';
import type { 
    QuickAnalysisResult, 
    EnhancedQuickAnalysisResult, 
    DetailedAnalysisResult, 
    EnhancedDetailedAnalysisResult,
    CancellationToken 
} from '../../../types/analysis-results';

/**
 * Service responsible for detailed package analysis
 * Handles comprehensive resource analysis and deep inspection
 */
export class DetailedAnalysisService {
    private resourceProcessor: ResourceProcessor;
    private enhancedMetadataService: EnhancedMetadataExtractionService;
    private contentAnalysisService: ContentAnalysisService;

    constructor() {
        this.resourceProcessor = new ResourceProcessor();
        this.enhancedMetadataService = new EnhancedMetadataExtractionService();
        this.contentAnalysisService = new ContentAnalysisService();
    }

    /**
     * Performs detailed synchronous analysis
     */
    public analyze(buffer: Buffer, filePath: string, quickResult: QuickAnalysisResult): DetailedAnalysisResult {
        if (!quickResult.needsDeepAnalysis) {
            return this.createBasicResult(buffer, filePath, quickResult);
        }

        return this.performDeepAnalysis(buffer, filePath, quickResult);
    }

    /**
     * Performs detailed asynchronous analysis with enhanced features
     */
    public async analyzeAsync(
        buffer: Buffer, 
        filePath: string, 
        quickResult: EnhancedQuickAnalysisResult, 
        cancellationToken?: CancellationToken
    ): Promise<EnhancedDetailedAnalysisResult> {
        const startTime = Date.now();
        
        try {
            if (cancellationToken?.isCancelled) {
                throw new Error('Operation cancelled');
            }

            if (!quickResult.needsDeepAnalysis) {
                const basicResult = await this.createBasicResultAsync(buffer, filePath, quickResult);

                // PHASE 4A: Add intelligence analysis even for basic results
                if (cancellationToken?.isCancelled) {
                    throw new Error('Operation cancelled');
                }

                // Create S4TK package for Resource Intelligence even in basic analysis
                let s4tkPackage: Package | undefined;
                if (filePath.endsWith('.package')) {
                    try {
                        s4tkPackage = Package.from(buffer);
                    } catch (error) {
                        // If package parsing fails, continue without it
                        s4tkPackage = undefined;
                    }
                }

                const intelligence = await this.performIntelligenceAnalysis(buffer, filePath, s4tkPackage);

                return {
                    ...basicResult,
                    specializedResources: [],
                    resourceValidation: { isValid: true, issues: [] },
                    performanceMetrics: {
                        totalTime: Date.now() - startTime,
                        quickAnalysisTime: quickResult.processingTime,
                        detailedAnalysisTime: 0,
                        resourceCount: quickResult.resourceCount
                    },
                    intelligence
                };
            }

            const detailedResult = await this.performDeepAnalysisAsync(buffer, filePath, quickResult, cancellationToken);
            
            return {
                ...detailedResult,
                performanceMetrics: {
                    totalTime: Date.now() - startTime,
                    quickAnalysisTime: quickResult.processingTime,
                    detailedAnalysisTime: Date.now() - startTime - quickResult.processingTime,
                    resourceCount: quickResult.resourceCount
                }
            };

        } catch (error) {
            const errorInfo = errorHandler.handleS4TKError(error, 'DetailedAnalysisService.analyzeAsync', filePath);
            
            return this.createErrorResult(buffer, filePath, errorInfo.message, Date.now() - startTime);
        }
    }

    /**
     * Creates basic result when detailed analysis isn't needed
     */
    private createBasicResult(buffer: Buffer, filePath: string, quickResult: QuickAnalysisResult): DetailedAnalysisResult {
        const fileMetadata = FileTypeDetector.analyzeFile(filePath, buffer.length);
        
        return {
            filePath,
            fileType: fileMetadata.type as FileType,
            category: quickResult.category,
            subcategory: fileMetadata.subcategory,
            fileSize: buffer.length,
            resourceCount: quickResult.resourceCount,
            isOverride: quickResult.hasConflictPotential,
            resources: [],
            dependencies: [],
            conflicts: [],
            metadata: {
                resourceTypes: quickResult.resourceTypes.map(type => this.getResourceTypeName(type)),
                analysisType: 'basic',
                analysisDate: new Date().toISOString()
            }
        } as DetailedAnalysisResult;
    }

    /**
     * Creates basic result asynchronously with enhanced metadata extraction
     */
    private async createBasicResultAsync(buffer: Buffer, filePath: string, quickResult: EnhancedQuickAnalysisResult): Promise<DetailedAnalysisResult> {
        const fileMetadata = FileTypeDetector.analyzeFile(filePath, buffer.length);

        // PHASE 3A: Enhanced metadata extraction for basic results
        const filenameMetadata = FilenameMetadataExtractor.extractFromFilename(filePath);

        return {
            filePath,
            fileType: fileMetadata.type as FileType,
            category: quickResult.category,
            subcategory: fileMetadata.subcategory,
            fileSize: buffer.length,
            resourceCount: quickResult.resourceCount,
            isOverride: quickResult.hasConflictPotential,
            resources: [],
            dependencies: [],
            conflicts: [],
            metadata: {
                resourceTypes: quickResult.resourceTypes.map(type => this.getResourceTypeName(type)),
                analysisType: 'basic_async',
                analysisDate: new Date().toISOString(),
                compressionStats: quickResult.compressionStats,
                validationIssues: quickResult.validationIssues,
                // PHASE 3A: Enhanced metadata extraction
                author: filenameMetadata.author,
                version: filenameMetadata.version,
                modName: filenameMetadata.modName,
                metadataSource: filenameMetadata.author ? 'filename' : undefined,
                metadataConfidence: filenameMetadata.author ? 95 : 0
            }
        } as DetailedAnalysisResult;
    }

    /**
     * Performs deep analysis synchronously
     */
    private performDeepAnalysis(buffer: Buffer, filePath: string, quickResult: QuickAnalysisResult): DetailedAnalysisResult {
        try {
            // Special handling for script files
            if (quickResult.fileType === FileType.SCRIPT) {
                return this.performScriptAnalysis(buffer, filePath, quickResult);
            }

            const resourceFilter = this.getDetailedResourceFilter(quickResult.category);

            const s4tkPackage = Package.from(buffer, {
                resourceFilter,
                decompressBuffers: true,
                loadRaw: false
            });

            const processedResources = this.resourceProcessor.process(s4tkPackage);

            // PHASE 3A: Extract metadata from multiple sources
            const filenameMetadata = FilenameMetadataExtractor.extractFromFilename(filePath);
            const manifestMetadata = ManifestAnalyzer.extractFromPackage(s4tkPackage);

            const deepAnalysis = DeepAnalyzer.analyzeResources(
                Array.from(s4tkPackage.entries.values()) as ResourceEntry[],
                filePath,
                FileTypeDetector.analyzeFile(filePath, buffer.length).type as FileType
            );

            const result: DetailedAnalysisResult = {
                filePath,
                fileType: FileType.PACKAGE,
                category: deepAnalysis.primaryCategory,
                subcategory: deepAnalysis.subcategory,
                suggestedPath: deepAnalysis.suggestedPath,
                fileSize: buffer.length,
                resourceCount: processedResources.length,
                isOverride: this.detectOverrideFromResources(processedResources, filePath),
                resources: processedResources,
                dependencies: [],
                conflicts: [],
                metadata: {
                    resourceTypes: quickResult.resourceTypes.map(type => this.getResourceTypeName(type)),
                    analysisType: 'detailed',
                    analysisDate: new Date().toISOString(),
                    deepAnalysis,
                    // PHASE 3A: Enhanced metadata extraction
                    author: manifestMetadata.author || filenameMetadata.author,
                    version: manifestMetadata.version || filenameMetadata.version,
                    modName: manifestMetadata.modName || filenameMetadata.modName,
                    description: manifestMetadata.description,
                    metadataSource: manifestMetadata.confidence > filenameMetadata.confidence ?
                        manifestMetadata.source : 'filename',
                    metadataConfidence: Math.max(manifestMetadata.confidence, filenameMetadata.confidence)
                }
            };

            this.detectDependenciesAndConflicts(result);
            return result;

        } catch (error) {
            errorHandler.handleS4TKError(error, 'DetailedAnalysisService.performDeepAnalysis', filePath);
            return this.createBasicResult(buffer, filePath, quickResult);
        }
    }

    /**
     * Performs specialized analysis for script files
     */
    private performScriptAnalysis(buffer: Buffer, filePath: string, quickResult: QuickAnalysisResult): DetailedAnalysisResult {
        try {
            // Use ScriptAnalyzer to extract detailed script information
            const scriptInfo = ScriptAnalyzer.analyzeScript(buffer, filePath);

            return {
                filePath,
                fileType: quickResult.fileType,
                category: quickResult.category,
                subcategory: scriptInfo.subcategory,
                fileSize: buffer.length,
                resourceCount: 1, // Script files contain one logical resource
                isOverride: false,
                resources: [{
                    id: 'script-content',
                    type: 'Script',
                    size: buffer.length,
                    compressed: false,
                    metadata: {
                        scriptInfo,
                        specialized: true,
                        processorUsed: 'ScriptAnalyzer'
                    },
                    issues: []
                }],
                dependencies: scriptInfo.dependencies,
                conflicts: scriptInfo.conflicts,
                metadata: {
                    scriptInfo,
                    analysisType: 'script',
                    specialized: true,
                    // PHASE 3A: Enhanced metadata extraction for scripts
                    author: scriptInfo.author,
                    version: scriptInfo.version,
                    modName: scriptInfo.description?.split(' ')[0], // Extract mod name from description
                    metadataSource: 'filename', // Scripts primarily use filename analysis
                    metadataConfidence: scriptInfo.author ? 95 : 0 // High confidence if author found
                },
                analysisTime: 0, // Will be set by caller
                issues: []
            };

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);

            return {
                filePath,
                fileType: quickResult.fileType,
                category: quickResult.category,
                subcategory: 'error',
                fileSize: buffer.length,
                resourceCount: 0,
                isOverride: false,
                resources: [],
                dependencies: [],
                conflicts: [],
                metadata: {
                    error: errorMessage,
                    analysisType: 'script'
                },
                analysisTime: 0,
                issues: [`Script analysis failed: ${errorMessage}`]
            };
        }
    }

    /**
     * Performs deep analysis asynchronously
     */
    private async performDeepAnalysisAsync(
        buffer: Buffer, 
        filePath: string, 
        quickResult: EnhancedQuickAnalysisResult, 
        cancellationToken?: CancellationToken
    ): Promise<EnhancedDetailedAnalysisResult> {
        try {
            if (cancellationToken?.isCancelled) {
                throw new Error('Operation cancelled');
            }

            const loadingOptions: SelectiveLoadingOptions = {
                analysisType: 'detailed',
                category: quickResult.category,
                enableSpecializedParsing: true,
                enableValidation: true
            };

            const s4tkPackage = await packageManager.loadPackageSelective(buffer, loadingOptions);
            
            if (cancellationToken?.isCancelled) {
                throw new Error('Operation cancelled');
            }

            const processedResources = this.resourceProcessor.process(s4tkPackage);
            
            const deepAnalysis = DeepAnalyzer.analyzeResources(
                Array.from(s4tkPackage.entries.values()) as ResourceEntry[],
                filePath,
                FileTypeDetector.analyzeFile(filePath, buffer.length).type as FileType
            );

            // PHASE 2: Enhanced multi-source metadata extraction
            const enhancedMetadataResult = await this.enhancedMetadataService.extractMetadata(
                buffer,
                filePath,
                {
                    enabledExtractors: {
                        filename: true,
                        stringTable: true,
                        tuning: true,
                        simData: true,
                        manifest: true
                    }
                }
            );

            const result: EnhancedDetailedAnalysisResult = {
                filePath,
                fileType: FileType.PACKAGE,
                category: deepAnalysis.primaryCategory,
                subcategory: deepAnalysis.subcategory,
                suggestedPath: deepAnalysis.suggestedPath,
                fileSize: buffer.length,
                resourceCount: processedResources.length,
                isOverride: this.detectOverrideFromResources(processedResources, filePath),
                resources: processedResources,
                dependencies: [],
                conflicts: [],
                metadata: {
                    resourceTypes: quickResult.resourceTypes.map(type => this.getResourceTypeName(type)),
                    analysisType: 'detailed_async',
                    analysisDate: new Date().toISOString(),
                    deepAnalysis,
                    compressionStats: quickResult.compressionStats,
                    // PHASE 2: Enhanced multi-source metadata extraction
                    author: enhancedMetadataResult.metadata.author,
                    version: enhancedMetadataResult.metadata.version,
                    modName: enhancedMetadataResult.metadata.modName,
                    description: enhancedMetadataResult.metadata.description,
                    downloadUrl: enhancedMetadataResult.metadata.downloadUrl,
                    requirements: enhancedMetadataResult.metadata.requirements,
                    installationGuidelines: enhancedMetadataResult.metadata.installationGuidelines,
                    metadataSource: enhancedMetadataResult.metadata.primarySource,
                    metadataConfidence: enhancedMetadataResult.metadata.overallConfidence,
                    sourceCount: enhancedMetadataResult.metadata.sourceCount,
                    hasConflicts: enhancedMetadataResult.metadata.hasConflicts,
                    qualityScore: enhancedMetadataResult.metadata.qualityScore,
                    sourceTypes: enhancedMetadataResult.metadata.sourceTypes
                },
                specializedResources: [], // Will be populated by specialized analyzers
                resourceValidation: { isValid: true, issues: [] },
                performanceMetrics: {
                    totalTime: 0, // Will be set by caller
                    quickAnalysisTime: 0, // Will be set by caller
                    detailedAnalysisTime: 0, // Will be set by caller
                    resourceCount: processedResources.length
                },
                // PHASE 2: Enhanced metadata extraction results
                enhancedMetadata: enhancedMetadataResult.metadata,
                extractionDetails: enhancedMetadataResult.extractionDetails,
                actualModName: enhancedMetadataResult.metadata.modName,
                actualDescription: enhancedMetadataResult.metadata.description,
                extractedItemNames: [], // Will be populated by content analysis
                metadataConfidence: enhancedMetadataResult.metadata.overallConfidence,
                hasStringTable: enhancedMetadataResult.extractionDetails.stringTable.successful
            };

            // PHASE 4A: Add intelligence analysis
            if (cancellationToken?.isCancelled) {
                throw new Error('Operation cancelled');
            }

            const intelligence = await this.performIntelligenceAnalysis(buffer, filePath, s4tkPackage);
            result.intelligence = intelligence;

            this.detectDependenciesAndConflicts(result);
            return result;

        } catch (error) {
            const errorInfo = errorHandler.handleS4TKError(error, 'DetailedAnalysisService.performDeepAnalysisAsync', filePath);
            throw error;
        }
    }

    /**
     * Creates error result for failed analysis
     */
    private createErrorResult(buffer: Buffer, filePath: string, errorMessage: string, totalTime: number): EnhancedDetailedAnalysisResult {
        const fileMetadata = FileTypeDetector.analyzeFile(filePath, buffer.length);
        
        return {
            filePath,
            fileType: fileMetadata.type as FileType,
            category: ModCategory.UNKNOWN,
            subcategory: 'error',
            fileSize: buffer.length,
            resourceCount: 0,
            isOverride: false,
            resources: [],
            dependencies: [],
            conflicts: [],
            metadata: { error: errorMessage },
            specializedResources: [],
            resourceValidation: { isValid: false, issues: [errorMessage] },
            performanceMetrics: {
                totalTime,
                quickAnalysisTime: 0,
                detailedAnalysisTime: 0,
                resourceCount: 0
            }
        } as EnhancedDetailedAnalysisResult;
    }

    /**
     * Gets resource filter for detailed analysis based on category
     */
    private getDetailedResourceFilter(category: ModCategory): (type: number, group: number, instance: bigint) => boolean {
        // Implementation would be moved from PackageAnalysisService
        return () => true; // Placeholder
    }

    /**
     * Detects if package contains override resources
     */
    private detectOverrideFromResources(resources: any[], filePath: string): boolean {
        // Implementation would be moved from PackageAnalysisService
        return false; // Placeholder
    }

    /**
     * Detects dependencies and conflicts
     */
    private detectDependenciesAndConflicts(result: DetailedAnalysisResult): void {
        // Implementation would be moved from PackageAnalysisService
    }

    /**
     * Gets resource type name
     */
    private getResourceTypeName(type: number): string {
        // Implementation would be moved from PackageAnalysisService
        return `Type_${type}`;
    }

    /**
     * Performs intelligence analysis using the new analyzers
     * PHASE 4A: Integration of intelligence components
     */
    private async performIntelligenceAnalysis(
        buffer: Buffer,
        filePath: string,
        s4tkPackage?: Package
    ): Promise<{
        dependencies: any;
        resourceIntelligence: any;
        qualityAssessment: any;
    }> {
        try {
            const fileName = filePath.split(/[/\\]/).pop() || 'unknown';
            const isScript = filePath.endsWith('.ts4script');

            let dependencies, resourceIntelligence, qualityAssessment;

            if (isScript) {
                // Script file analysis with Script Intelligence
                dependencies = DependencyAnalyzer.analyzeScriptDependencies(buffer, fileName);
                resourceIntelligence = ScriptIntelligenceAnalyzer.analyzeScriptIntelligence(buffer, fileName);
                qualityAssessment = QualityAssessmentAnalyzer.assessQuality(buffer, fileName, false);
            } else if (s4tkPackage) {
                // Package file analysis with enhanced content analysis
                dependencies = DependencyAnalyzer.analyzePackageDependencies(s4tkPackage, fileName);
                resourceIntelligence = ResourceIntelligenceAnalyzer.analyzePackageIntelligence(s4tkPackage, fileName);
                qualityAssessment = QualityAssessmentAnalyzer.assessQuality(buffer, fileName, true);

                // ENHANCEMENT: Add content analysis for comprehensive mod understanding
                try {
                    const contentAnalysis = this.contentAnalysisService.analyzeModContent(buffer, fileName);

                    // Integrate content analysis into intelligence results
                    if (resourceIntelligence) {
                        resourceIntelligence.contentAnalysis = contentAnalysis;
                    } else {
                        resourceIntelligence = { contentAnalysis };
                    }
                } catch (error) {
                    console.warn('[DetailedAnalysisService] Content analysis failed:', error);
                }
            } else {
                // Fallback for unknown file types
                dependencies = null;
                resourceIntelligence = null;
                qualityAssessment = null;
            }

            return {
                dependencies,
                resourceIntelligence,
                qualityAssessment
            };
        } catch (error) {
            // Graceful fallback - intelligence analysis is optional
            console.warn(`Intelligence analysis failed for ${filePath}:`, error);
            return {
                dependencies: null,
                resourceIntelligence: null,
                qualityAssessment: null
            };
        }
    }

    /**
     * Analyze StringTable resources to extract mod metadata
     * PHASE 1: StringTable Analysis Implementation
     */
    private async analyzeStringTableResources(resources: ResourceEntry[]): Promise<StringTableData | undefined> {
        try {
            // Find StringTable resources
            const stblResources = resources.filter(r => r.key.type === BinaryResourceType.StringTable);

            if (stblResources.length === 0) {
                return undefined;
            }

            // Process the first StringTable resource (most mods have only one)
            const stringTableProcessor = new StringTableProcessor();
            const stringTableData = await stringTableProcessor.processStringTable(stblResources[0]);

            return stringTableData;

        } catch (error) {
            console.warn('StringTable analysis failed:', error);
            return undefined;
        }
    }
}

// Export singleton instance
export const detailedAnalysisService = new DetailedAnalysisService();