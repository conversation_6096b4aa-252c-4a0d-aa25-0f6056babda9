import { Package } from '@s4tk/models';
import type { PackageFileReadingOptions, ResourceEntry } from '@s4tk/models/types';
import { ModCategory } from '../../types/analysis';
import { RESOURCE_GROUPS, ResourceTypeUtils } from '../../constants/ResourceTypeRegistry';
import { UnifiedErrorHandler, UnifiedErrorCategory } from '../shared/UnifiedErrorHandler';
import { PackageParserService } from '../shared/PackageParserService';

/**
 * Options for selective package loading
 */
export interface SelectiveLoadingOptions extends PackageFileReadingOptions {
    analysisType: 'quick' | 'detailed' | 'conflict';
    category?: ModCategory;
    enableSpecializedParsing: boolean;
    enableValidation: boolean;
}

/**
 * Package validation result
 */
export interface PackageValidationResult {
    isValid: boolean;
    fileSize: number;
    resourceCount: number;
    issues: string[];
    compressionTypes: Record<string, number>;
}

/**
 * Service for managing S4TK Package operations with optimized loading
 */
export class PackageManager {
    
    /**
     * Loads a package with selective resource loading based on analysis type and category
     * @param buffer - Package buffer to load
     * @param options - Selective loading options
     * @returns Promise resolving to loaded Package
     */
    public async loadPackageSelective(buffer: Buffer, options: SelectiveLoadingOptions): Promise<Package> {
        try {
            const packageOptions = this.buildPackageOptions(options);
            
            // Use async loading for better performance
            const s4tkPackage = await Package.fromAsync(buffer, packageOptions);

            if (options.enableValidation) {
                await this.validatePackageResources(s4tkPackage);
            }

            return s4tkPackage;
            
        } catch (error) {
            const errorInfo = errorHandler.handleS4TKError(
                error, 
                `loadPackageSelective(${options.analysisType})`, 
                'buffer'
            );
            
            if (!errorInfo.recoverable) {
                throw error;
            }
            
            // Try fallback loading without options
            console.warn('Falling back to basic package loading');
            return await Package.fromAsync(buffer);
        }
    }
    
    /**
     * Extracts resources asynchronously with filtering
     * @param buffer - Package buffer
     * @param options - Package reading options
     * @returns Promise resolving to resource entries
     */
    public async extractResourcesAsync(buffer: Buffer, options: PackageFileReadingOptions): Promise<ResourceEntry[]> {
        try {
            return await Package.extractResourcesAsync(buffer, options);
        } catch (error) {
            errorHandler.handleS4TKError(
                error, 
                'extractResourcesAsync', 
                'buffer'
            );
            
            // Fallback to synchronous extraction
            console.warn('Falling back to synchronous resource extraction');
            return Package.extractResources(buffer, options);
        }
    }
    
    /**
     * Validates a package and its resources using shared services
     * @param buffer - Package buffer to validate
     * @returns Promise resolving to validation result
     */
    public async validatePackage(buffer: Buffer): Promise<PackageValidationResult> {
        const result: PackageValidationResult = {
            isValid: true,
            fileSize: buffer.length,
            resourceCount: 0,
            issues: [],
            compressionTypes: {}
        };

        try {
            // Use shared package validation
            const sharedValidation = PackageParserService.validatePackageStructure(buffer);

            if (!sharedValidation.isValid) {
                result.isValid = false;
                result.issues.push(...sharedValidation.errors);
                return result;
            }

            result.resourceCount = sharedValidation.resourceCount;

            // Additional async validation if needed
            const s4tkPackage = await Package.fromAsync(buffer, {
                loadRaw: true,
                decompressBuffers: false,
                limit: 1000 // Limit for validation
            });

            // Validate resources
            for (const entry of s4tkPackage.entries.values()) {
                this.validateResource(entry, result);
            }

        } catch (error) {
            result.isValid = false;
            const unifiedError = UnifiedErrorHandler.createError(
                error,
                'PackageManager.validatePackage',
                'buffer',
                UnifiedErrorCategory.PACKAGE_LOADING
            );
            result.issues.push(unifiedError.message);
        }

        return result;
    }
    
    /**
     * Builds package options based on selective loading configuration
     * @param options - Selective loading options
     * @returns Package file reading options
     */
    private buildPackageOptions(options: SelectiveLoadingOptions): PackageFileReadingOptions {
        const packageOptions: PackageFileReadingOptions = {
            decompressBuffers: options.enableSpecializedParsing,
            loadRaw: !options.enableSpecializedParsing,
        };
        
        // Set resource filter based on analysis type
        switch (options.analysisType) {
            case 'quick':
                packageOptions.resourceFilter = (type: number) => 
                    ResourceTypeHelpers.isEssentialForQuickAnalysis(type);
                packageOptions.limit = 50;
                break;
                
            case 'detailed':
                if (options.category) {
                    packageOptions.resourceFilter = this.getCategoryResourceFilter(options.category);
                }
                break;
                
            case 'conflict':
                packageOptions.resourceFilter = (type: number) =>
                    RESOURCE_GROUPS.CRITICAL_RESOURCES.includes(type);
                packageOptions.limit = 100;
                break;
        }
        
        return packageOptions;
    }
    
    /**
     * Gets resource filter for specific mod category
     * @param category - Mod category
     * @returns Resource filter function
     */
    private getCategoryResourceFilter(category: ModCategory): (type: number, group: number, instance: bigint) => boolean {
        switch (category) {
            case ModCategory.CAS_CC:
                return (type: number) =>
                    RESOURCE_GROUPS.CAS_RESOURCES.includes(type) ||
                    RESOURCE_GROUPS.TEXTURE_RESOURCES.includes(type);

            case ModCategory.BUILD_BUY_CC:
                return (type: number) =>
                    RESOURCE_GROUPS.OBJECT_RESOURCES.includes(type) ||
                    RESOURCE_GROUPS.TEXTURE_RESOURCES.includes(type) ||
                    RESOURCE_GROUPS.MESH_RESOURCES.includes(type);

            case ModCategory.TUNING_MOD:
            case ModCategory.OVERRIDE:
                return (type: number) =>
                    RESOURCE_GROUPS.TUNING_RESOURCES.includes(type) ||
                    RESOURCE_GROUPS.CRITICAL_RESOURCES.includes(type);
                    
            default:
                return () => true; // Load all resources for unknown categories
        }
    }
    
    /**
     * Validates individual resource
     * @param entry - Resource entry to validate
     * @param result - Validation result to update
     */
    private validateResource(entry: ResourceEntry, result: PackageValidationResult): void {
        try {
            // Validate resource key
            if (!entry.key || typeof entry.key.type !== 'number') {
                result.issues.push(`Invalid resource key: ${entry.id}`);
                result.isValid = false;
                return;
            }
            
            // Validate resource buffer
            if (!entry.value?.buffer || entry.value.buffer.length === 0) {
                result.issues.push(`Empty resource buffer: ${entry.id}`);
                result.isValid = false;
                return;
            }
            
            // Track compression types
            if (entry.value?.compressionType !== undefined) {
                const compressionName = this.getCompressionTypeName(entry.value.compressionType);
                result.compressionTypes[compressionName] = (result.compressionTypes[compressionName] || 0) + 1;
                
                // Validate compression type
                const validCompressionTypes = [0x0000, 0xFFFE, 0xFFFF, 0xFFE0, 0x5A42];
                if (!validCompressionTypes.includes(entry.value.compressionType)) {
                    result.issues.push(`Unknown compression type: 0x${entry.value.compressionType.toString(16)} in resource ${entry.id}`);
                }
            }
            
        } catch (error) {
            result.issues.push(`Resource validation error: ${error.message}`);
            result.isValid = false;
        }
    }
    
    /**
     * Validates package resources after loading
     * @param s4tkPackage - Loaded package to validate
     */
    private async validatePackageResources(s4tkPackage: Package): Promise<void> {
        let validationErrors = 0;

        for (const entry of s4tkPackage.entries.values()) {
            try {
                // Basic validation checks
                if (!entry.key || !entry.value) {
                    validationErrors++;
                    continue;
                }
                
                // Check for reasonable resource sizes
                if (entry.value.buffer && entry.value.buffer.length > 100 * 1024 * 1024) { // 100MB limit
                    console.warn(`Large resource detected: ${entry.id} (${entry.value.buffer.length} bytes)`);
                }
                
            } catch (error) {
                validationErrors++;
                console.warn(`Resource validation failed for ${entry.id}:`, error);
            }
        }
        
        if (validationErrors > 0) {
            console.warn(`Package validation completed with ${validationErrors} errors`);
        }
    }
    
    /**
     * Gets human-readable compression type name
     * @param compressionType - Compression type number
     * @returns Compression type name
     */
    private getCompressionTypeName(compressionType: number): string {
        switch (compressionType) {
            case 0x0000: return 'Uncompressed';
            case 0xFFFE: return 'Streamable';
            case 0xFFFF: return 'Internal';
            case 0xFFE0: return 'Deleted';
            case 0x5A42: return 'ZLIB';
            default: return `Unknown (0x${compressionType.toString(16)})`;
        }
    }
}

// Export singleton instance
export const packageManager = new PackageManager();